<!-- 动态 -->
<template>
  <view class="dynamics-layout">
    <!-- 统计信息和筛选 -->
    <view class="stats-header">
      <text class="stats-text">全部动态 {{ dynamicsList.length }}</text>
      <view class="filter-btn" @click="showFilter">
        <image
          class="filter-icon"
          src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/filter.svg"
          mode="aspectFit"
        />
      </view>
    </view>

    <!-- 月份标题 -->
    <view class="month-title">{{ currentMonth }}月</view>

    <!-- 时间轴动态列表 -->
    <view v-if="dynamicsList.length > 0" class="timeline-list">
      <view v-for="(item, index) in dynamicsList" :key="index" class="timeline-item">
        <!-- 时间轴左侧图标 -->
        <view class="timeline-icon">
          <view class="icon-circle" :class="getIconClass(item.recordType)">
            <image
              v-if="getIconUrl(item.recordType)"
              :src="getIconUrl(item.recordType)"
              class="icon-image"
              mode="aspectFit"
            />
          </view>
          <view v-if="index < dynamicsList.length - 1" class="timeline-line"></view>
        </view>

        <!-- 时间轴右侧内容 -->
        <view class="timeline-content">
          <view class="content-header">
            <text class="record-type" :class="getTitleColorClass(item.recordType)">{{
              item.recordTypeDesc
            }}</text>
            <text class="record-time">{{ formatTime(item.recordTime) }}</text>
          </view>

          <view class="content-body">
            <!-- 观察记录内容 -->
            <template v-if="item.recordType === 0">
              <view class="content-title">活动：{{ item.content.activityName || '未知活动' }}</view>
              <view class="content-desc">
                <text>课程：{{ item.content.observationLocation || '未知地点' }}</text>
              </view>
              <view v-if="item.content.picUrlList" class="content-images">
                <image
                  :src="item.content.picUrlList"
                  class="content-image"
                  mode="aspectFill"
                  @click="previewImage(item.content.picUrlList, [item.content.picUrlList])"
                />
              </view>
            </template>

            <!-- 儿童进区内容 -->
            <template v-if="item.recordType === 2">
              <view class="content-title" v-if="item.content.areaAlias"
                >已进入{{ item.content.area }}({{ item.content.areaAlias }})</view
              >
              <view v-else class="content-title"
                >已进入{{ item.content.area }}</view
              >
              <view class="content-desc">
                <!-- <view>观察场所：学习</view> -->
                <!-- <view>进区时间：{{ formatDate(item.recordTime) }}</view> -->
                <!-- <view>观察地点：{{ item.content.area || '未知' }}</view>
                <view>观察班级：小一班</view>
                <view>观察对象：{{ userInfo.title || '未知' }}</view> -->
              </view>
            </template>

            <!-- 表征作品/倾听记录内容 -->
            <template v-if="item.recordType === 1">
              <view class="content-title">{{ item.content.title || '表征作品/倾听记录' }}</view>
              <view class="content-desc">
                <view v-if="item.content.activityLocation"
                  >活动地点：{{ item.content.activityLocation }}</view
                >
                <view v-if="item.content.audioText">倾听内容：{{ item.content.audioText }}</view>
                <view>记录时间：{{ formatDate(item.recordTime) }}</view>
              </view>
            </template>

            <!-- 照片/视频/音频内容 -->
            <template v-if="item.recordType === 4">
              <!-- <view class="content-title">{{ item.recordTypeDesc }}</view> -->
              <view class="content-desc">
                <view>记录时间：{{ formatDate(item.recordTime) }}</view>
                <view v-if="item.content && item.content.list && item.content.list.length > 0">
                  共{{ item.content.list.length }}个文件
                </view>
              </view>
              <!-- 媒体文件展示 -->
              <view v-if="item.content && item.content.list && item.content.list.length > 0" class="media-list">
                <view
                  v-for="(media, mediaIndex) in item.content.list.slice(0, 3)"
                  :key="mediaIndex"
                  class="media-item"
                  :class="{
                    'video-item': isVideoFile(media.name),
                    'file-item-container': isOtherFile(media.name)
                  }"
                  @click="previewMedia(media, item.content.list)"
                >
                  <!-- 视频文件 -->
                  <template v-if="isVideoFile(media.name)">
                    <video
                      :id="`video_${item.recordId}_${mediaIndex}`"
                      :src="media.url"
                      class="media-video"
                      :poster="`${media.url}?x-oss-process=video/snapshot,t_800`"
                      controls
                      show-mute-btn
                      show-fullscreen-btn
                      show-center-play-btn
                      object-fit="contain"
                      auto-pause-if-open-native
                      auto-pause-if-navigate
                      enable-play-gesture
                      :direction="0"
                      @play="onVideoPlay(`video_${item.recordId}_${mediaIndex}`)"
                      @pause="onVideoPause(`video_${item.recordId}_${mediaIndex}`)"
                      @ended="onVideoEnded(`video_${item.recordId}_${mediaIndex}`)"
                      @fullscreenchange="onFullscreenChange"
                      @error="onVideoError"
                    />
                  </template>
                  <!-- 图片文件 -->
                  <template v-else-if="isImageFile(media.name)">
                    <image
                      :src="media.url"
                      class="media-image"
                      mode="aspectFill"
                    />
                  </template>
                  <!-- 其他文件（PDF、文档等） -->
                  <template v-else>
                    <view class="file-item">
                      <image
                        src="https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/dynamics/file.svg"
                        class="file-icon"
                        mode="aspectFit"
                      />
                      <view class="file-info">
                        <text class="file-name">{{ getFileName(media.name) }}</text>
                        <text class="file-ext">{{ getFileExtension(media.name) }}</text>
                      </view>
                    </view>
                  </template>
                </view>
                <view v-if="item.content.list.length > 3" class="more-media">
                  +{{ item.content.list.length - 3 }}
                </view>
              </view>
            </template>

            <!-- 儿童心情内容 -->
            <template v-if="item.recordType === 3">
              <!-- <view class="content-title">{{ item.recordTypeDesc }}</view> -->
              <view class="content-desc">
                <view v-if="item.content.moodName">心情：{{ item.content.moodName }}</view>
                <view>记录时间：{{ formatDate(item.recordTime) }}</view>
              </view>
              <!-- 心情图片展示 -->
              <view v-if="item.content.imageUrl" class="mood-image">
                <image
                  :src="item.content.imageUrl"
                  class="mood-img"
                  mode="aspectFit"
                />
              </view>
            </template>

            <!-- 童言童语内容 -->
            <template v-if="item.recordType === 5">
              <view class="content-title">{{ item.recordTypeDesc }}</view>
              <view class="content-desc">
                <view v-if="item.content.name">活动名称：{{ item.content.name }}</view>
                <view>记录时间：{{ formatDate(item.recordTime) }}</view>
              </view>
              <!-- 童言童语内容展示 -->
              <view v-if="item.content.childTeacherInteraction" class="words-content">
                <text class="words-text">{{ formatChildWords(item.content.childTeacherInteraction) }}</text>
              </view>
            </template>

            <!-- 其他类型内容 -->
            <template v-if="![0, 1, 2, 3, 4, 5].includes(item.recordType)">
              <view class="content-title">{{ item.recordTypeDesc }}</view>
              <view class="content-desc">
                <text>记录时间：{{ formatDate(item.recordTime) }}</text>
              </view>
            </template>
          </view>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-else-if="!loading" class="empty-state">
      <up-empty mode="data" text="暂无动态内容" />
    </view>

    <!-- 加载状态 -->
    <view v-if="loading" class="loading-container">
      <up-loading-icon mode="spinner" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- 筛选弹窗 -->
    <up-popup :show="showFilterPopup" mode="bottom" round="20" @close="closeFilter">
      <view class="filter-popup">
        <view class="popup-header">
          <text class="popup-title">筛选条件</text>
          <view class="close-btn" @click="closeFilter">
            <text class="close-text">×</text>
          </view>
        </view>

        <view class="filter-content">
          <!-- 记录类型筛选 -->
          <view class="filter-item">
            <text class="filter-label">记录类型</text>
            <view class="type-options">
              <view
                v-for="option in recordTypeOptions"
                :key="option.value"
                class="type-option"
                :class="{ active: tempSelectedRecordTypes.includes(option.value) }"
                @click="toggleRecordType(option.value)"
              >
                <text class="option-text">{{ option.label }}</text>
                <text v-if="tempSelectedRecordTypes.includes(option.value)" class="check-icon"
                  >✓</text
                >
              </view>
            </view>
          </view>

          <!-- 日期范围筛选 -->
          <view class="filter-item">
            <text class="filter-label">日期范围</text>
            <uni-datetime-picker
              v-model="tempDateRange"
              type="daterange"
              :clear-icon="false"
              placeholder="请选择日期范围"
              @change="onDateChange"
            />
          </view>
        </view>

        <!-- 底部按钮 -->
        <view class="popup-footer">
          <view class="btn-group">
            <view class="btn btn-cancel" @click="resetFilter">重置</view>
            <view class="btn btn-confirm" @click="confirmFilter">确认</view>
          </view>
        </view>
      </view>
    </up-popup>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue'
import { getChildrenDynamicCount } from '@/api/api.js'

let props = defineProps({
  userInfo: {
    type: Object,
    default: () => {}
  }
})

// 数据状态
const dynamicsList = ref([])
const loading = ref(false)
const currentMonth = ref(new Date().getMonth() + 1)

// 筛选相关
const showFilterPopup = ref(false)
const selectedRecordTypes = ref([0, 1, 2, 3, 4, 5]) // 默认选择所有类型
const tempSelectedRecordTypes = ref([0, 1, 2, 3, 4, 5]) // 临时选择状态
const dateRange = ref([]) // 日期范围
const tempDateRange = ref([]) // 临时日期范围

// 记录类型选项
const recordTypeOptions = [
  { value: 0, label: '观察记录' },
  { value: 1, label: '表征作品/倾听记录' },
  { value: 2, label: '幼儿进区' },
  { value: 3, label: '儿童心情' },
  { value: 4, label: '照片/视频/音频' },
  { value: 5, label: '童言童语' }
]

// 获取当前月份的开始和结束日期
const getCurrentMonthRange = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth()

  const beginDate = new Date(year, month, 1)
  const endDate = new Date(year, month + 1, 0)

  return {
    beginDate: formatDate(beginDate),
    endDate: formatDate(endDate)
  }
}

// 格式化日期为 YYYY-MM-DD
const formatDate = (dateInput) => {
  if (!dateInput) return ''
  try {
    const date = dateInput instanceof Date ? dateInput : new Date(dateInput)
    if (isNaN(date.getTime())) return dateInput // 如果日期无效，返回原输入
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    return `${year}-${month}-${day}`
  } catch (error) {
    console.error('日期格式化错误:', error)
    return dateInput
  }
}

// 格式化时间显示
const formatTime = (timeStr) => {
  if (!timeStr) return ''
  const date = new Date(timeStr)
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${month}-${day} ${hours}:${minutes}`
}

// 预览图片
const previewImage = (current, urls) => {
  uni.previewImage({
    current,
    urls
  })
}

// 判断是否为视频文件
const isVideoFile = (fileName) => {
  if (!fileName) return false
  const videoExtensions = ['.mp4', '.avi', '.mov', '.wmv', '.flv', '.webm', '.m4v']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return videoExtensions.includes(extension)
}

// 判断是否为图片文件
const isImageFile = (fileName) => {
  if (!fileName) return false
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg']
  const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'))
  return imageExtensions.includes(extension)
}

// 判断是否为其他文件（非图片非视频）
const isOtherFile = (fileName) => {
  return !isImageFile(fileName) && !isVideoFile(fileName)
}

// 获取文件名（不含扩展名）
const getFileName = (fullName) => {
  if (!fullName) return '未知文件'
  const lastDotIndex = fullName.lastIndexOf('.')
  if (lastDotIndex === -1) return fullName
  return fullName.substring(0, lastDotIndex)
}

// 获取文件扩展名
const getFileExtension = (fileName) => {
  if (!fileName) return ''
  const lastDotIndex = fileName.lastIndexOf('.')
  if (lastDotIndex === -1) return ''
  return fileName.substring(lastDotIndex + 1).toUpperCase()
}

// 格式化童言童语内容
const formatChildWords = (content) => {
  if (!content) return ''

  // 移除@符号和儿童姓名，只保留实际的童言童语内容
  // 例如：@孙莞柠  童言童语测试啊\n@孙莞柠  123
  return content
    .split('\n')
    .map(line => {
      // 移除每行开头的@儿童姓名部分
      const atIndex = line.indexOf('@')
      if (atIndex !== -1) {
        const spaceIndex = line.indexOf('  ', atIndex) // 查找两个空格
        if (spaceIndex !== -1) {
          return line.substring(spaceIndex + 2).trim() // 获取空格后的内容
        }
      }
      return line.trim()
    })
    .filter(line => line.length > 0) // 过滤空行
    .join('\n')
}

// 当前播放的视频上下文
const currentVideoContext = ref(null)

// 视频播放事件
const onVideoPlay = (videoId) => {
  // 暂停其他正在播放的视频
  if (currentVideoContext.value && currentVideoContext.value.id !== videoId) {
    currentVideoContext.value.pause()
  }

  // 创建新的视频上下文
  const videoContext = uni.createVideoContext(videoId)
  videoContext.id = videoId
  currentVideoContext.value = videoContext
}

// 视频暂停事件
const onVideoPause = (videoId) => {
  console.log('视频暂停:', videoId)
}

// 视频播放结束事件
const onVideoEnded = (videoId) => {
  console.log('视频播放结束:', videoId)
  currentVideoContext.value = null
}

// 全屏状态变化
const onFullscreenChange = (e) => {
  console.log('全屏状态变化:', e.detail)
  // e.detail = {fullScreen, direction}
  // fullScreen: true表示进入全屏，false表示退出全屏
  // direction: vertical 或 horizontal
}

// 视频播放错误
const onVideoError = (e) => {
  console.error('视频播放错误:', e.detail)
  uni.showToast({
    title: '视频播放失败',
    icon: 'none'
  })
}

// 预览媒体文件
const previewMedia = (currentMedia, mediaList) => {
  // 如果是视频文件，不需要特殊处理，直接点击video组件即可播放
  if (isVideoFile(currentMedia.name)) {
    return
  }

  // 如果是其他文件（PDF、文档等），尝试下载或打开
  if (isOtherFile(currentMedia.name)) {
    downloadFile(currentMedia)
    return
  }

  // 只预览图片类型的文件
  const imageUrls = mediaList
    .filter(media => isImageFile(media.name))
    .map(media => media.url)

  if (imageUrls.length > 0) {
    uni.previewImage({
      current: currentMedia.url,
      urls: imageUrls
    })
  }
}

// 下载文件
const downloadFile = (media) => {
  uni.showActionSheet({
    itemList: ['预览文件', '保存到相册'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 预览文件
        uni.downloadFile({
          url: media.url,
          success: (downloadRes) => {
            if (downloadRes.statusCode === 200) {
              uni.openDocument({
                filePath: downloadRes.tempFilePath,
                success: () => {
                  console.log('打开文档成功')
                },
                fail: (err) => {
                  console.error('打开文档失败:', err)
                  uni.showToast({
                    title: '无法打开此文件',
                    icon: 'none'
                  })
                }
              })
            }
          },
          fail: (err) => {
            console.error('下载文件失败:', err)
            uni.showToast({
              title: '下载失败',
              icon: 'none'
            })
          }
        })
      } else if (res.tapIndex === 1) {
        // 保存到相册（仅适用于图片）
        uni.showToast({
          title: '此文件类型不支持保存到相册',
          icon: 'none'
        })
      }
    }
  })
}

// 获取图标样式类名
const getIconClass = (recordType) => {
  const iconMap = {
    0: 'icon-observation', // 观察记录
    1: 'icon-activity', // 活动记录
    2: 'icon-area', // 儿童进区
    3: 'icon-mood', // 心情
    4: 'icon-health', // 健康
    5: 'icon-other' // 其他
  }
  return iconMap[recordType] || 'icon-default'
}

// 获取图标URL
const getIconUrl = (recordType) => {
  const iconUrlMap = {
    0: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/gcjlnew.svg', // 观察记录
    1: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/bzzpqtjl.svg', // 表征作品/倾听记录
    2: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/rtjq.svg', // 儿童进区
    3: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/xqnew.svg', // 儿童心情
    4: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/imgnew.svg', // 照片/视频/音频
    5: 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/tytynew.svg'  // 童言童语
  }
  return iconUrlMap[recordType] || ''
}

// 获取标题颜色类名
const getTitleColorClass = (recordType) => {
  const colorMap = {
    0: 'title-observation', // 观察记录 - 绿色
    1: 'title-work',        // 表征作品/倾听记录 - 紫色
    2: 'title-area',        // 儿童进区 - 粉色
    3: 'title-mood',        // 儿童心情 - 红色
    4: 'title-photo',       // 照片/视频/音频 - 蓝色
    5: 'title-words'        // 童言童语 - 橙色
  }
  return colorMap[recordType] || ''
}

// 显示筛选弹窗
const showFilter = () => {
  // 同步当前状态到临时状态
  tempSelectedRecordTypes.value = [...selectedRecordTypes.value]
  tempDateRange.value = [...dateRange.value]
  showFilterPopup.value = true
}

// 关闭筛选弹窗
const closeFilter = () => {
  showFilterPopup.value = false
}

// 切换记录类型选择
const toggleRecordType = (type) => {
  const index = tempSelectedRecordTypes.value.indexOf(type)
  if (index > -1) {
    // 如果已选择，则取消选择
    tempSelectedRecordTypes.value.splice(index, 1)
  } else {
    // 如果未选择，则添加选择
    tempSelectedRecordTypes.value.push(type)
  }
}

// 日期变化
const onDateChange = (e) => {
  tempDateRange.value = e
}

// 重置筛选
const resetFilter = () => {
  tempSelectedRecordTypes.value = [0, 1, 2, 3, 4, 5]
  tempDateRange.value = []
}

// 确认筛选
const confirmFilter = () => {
  // 应用临时选择状态
  selectedRecordTypes.value = [...tempSelectedRecordTypes.value]
  dateRange.value = [...tempDateRange.value]

  closeFilter()
  // 这里可以触发筛选事件，通知父组件重新获取数据
  emit('filter', {
    recordTypes: selectedRecordTypes.value,
    dateRange: dateRange.value
  })
}

// 获取动态数据
const getDynamicsData = async () => {
  if (!props.userInfo.id) return

  loading.value = true
  try {
    const { beginDate, endDate } = getCurrentMonthRange()
    const res = await getChildrenDynamicCount({
      childId: props.userInfo.id,
      beginDate,
      endDate
    })

    if (res.status === 0 && res.data) {
      dynamicsList.value = res.data
    }
  } catch (error) {
    console.error('获取动态数据失败:', error)
    uni.showToast({
      title: '获取数据失败',
      icon: 'none',
      duration: 2000
    })
  } finally {
    loading.value = false
  }
}

// 监听userInfo变化
watch(
  () => props.userInfo,
  (newVal) => {
    if (newVal && newVal.id) {
      getDynamicsData()
    }
  },
  { deep: true, immediate: true }
)
</script>

<style lang="scss" scoped>
@import '@/common/css/index.scss';

.dynamics-layout {
  padding: 0 32rpx 32rpx 32rpx;
  box-sizing: border-box;
  min-height: 400rpx;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0 16rpx 0;

  .stats-text {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
  }

  .filter-btn {
    display: flex;
    align-items: center;
    padding: 8rpx;
    border-radius: 20rpx;

    .filter-icon {
      width: 32rpx;
      height: 32rpx;
    }
  }
}

.month-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 32rpx;
}

.timeline-list {
  .timeline-item {
    display: flex;
    position: relative;

    &:not(:last-child) {
      margin-bottom: 32rpx;
    }

    &:last-child {
      .timeline-icon .timeline-line {
        display: none;
      }
    }

    .timeline-icon {
      position: relative;
      margin-right: 24rpx;
      width: 48rpx;
      display: flex;
      flex-direction: column;
      align-items: center;

      .icon-circle {
        width: 48rpx;
        height: 48rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        z-index: 2;

        .icon-image {
          width: 48rpx;
          height: 48rpx;
        }
      }

      .timeline-line {
        width: 2rpx;
        background: #e5e5e7;
        position: absolute;
        top: 24rpx;
        left: 50%;
        transform: translateX(-50%);
        bottom: -24rpx;
        z-index: 1;
      }
    }

    .timeline-content {
      flex: 1;
      background: #fff;
      border-radius: 16rpx;
      padding: 24rpx;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

      .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16rpx;

        .record-type {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;

          &.title-observation {
            color: rgba(84, 186, 106, 1);
          }

          &.title-work {
            color: rgba(110, 116, 230, 1);
          }

          &.title-area {
            color: rgba(247, 106, 189, 1);
          }

          &.title-words {
            color: rgba(240, 145, 77, 1);
          }

          &.title-photo {
            color: rgba(96, 139, 240, 1);
          }

          &.title-mood {
            color: rgba(237, 111, 114, 1);
          }
        }

        .record-time {
          font-size: 24rpx;
          color: #8e8e93;
        }
      }

      .content-body {
        .content-title {
          font-size: 28rpx;
          color: #333;
          font-weight: 500;
          margin-bottom: 12rpx;
          line-height: 1.4;
        }

        .content-desc {
          font-size: 26rpx;
          color: #666;
          line-height: 1.5;

          view {
            margin-bottom: 8rpx;

            &:last-child {
              margin-bottom: 0;
            }
          }
        }

        .content-images {
          margin-top: 16rpx;

          .content-image {
            width: 200rpx;
            height: 200rpx;
            border-radius: 12rpx;
            background: #f5f5f5;
          }
        }

        .media-list {
          display: flex;
          flex-wrap: wrap;
          gap: 12rpx;
          margin-top: 16rpx;

          .media-item {
            border-radius: 8rpx;
            overflow: hidden;
            background: #f5f5f5;
            position: relative;
            width: 120rpx;
            height: 120rpx;

            // 视频项目样式
            &.video-item {
              width: 100%;
              height: 300rpx;
              margin-bottom: 12rpx;
            }

            // 文件项目样式
            &.file-item-container {
              width: 120rpx;
              height: 120rpx;
              background: #f8f9fa;
              border: 1rpx solid #e5e5e7;
            }

            .media-image {
              width: 100%;
              height: 100%;
            }

            .media-video {
              width: 100%;
              height: 100%;
              border-radius: 8rpx;
            }

            .file-item {
              width: 100%;
              height: 100%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              padding: 16rpx;
              box-sizing: border-box;

              .file-icon {
                width: 48rpx;
                height: 48rpx;
                margin-bottom: 8rpx;
              }

              .file-info {
                text-align: center;
                width: 100%;

                .file-name {
                  display: block;
                  font-size: 20rpx;
                  color: #333;
                  line-height: 1.2;
                  margin-bottom: 4rpx;
                  overflow: hidden;
                  text-overflow: ellipsis;
                  white-space: nowrap;
                }

                .file-ext {
                  display: block;
                  font-size: 18rpx;
                  color: #999;
                  font-weight: 500;
                }
              }
            }
          }

          .more-media {
            width: 120rpx;
            height: 120rpx;
            border-radius: 8rpx;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            font-size: 24rpx;
          }
        }

        .mood-image {
          margin-top: 16rpx;

          .mood-img {
            width: 120rpx;
            height: 120rpx;
            border-radius: 8rpx;
          }
        }

        .words-content {
          margin-top: 16rpx;
          padding: 24rpx;
          background: #f8f9fa;
          border-radius: 12rpx;
          border-left: 4rpx solid rgba(240, 145, 77, 1);

          .words-text {
            font-size: 28rpx;
            color: #333;
            line-height: 1.6;
            white-space: pre-wrap;
            word-break: break-all;
          }
        }
      }
    }
  }
}

.empty-state {
  padding: 60rpx 0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;

  .loading-text {
    font-size: 24rpx;
    color: #999;
    margin-top: 16rpx;
  }
}

.filter-popup {
  padding: 40rpx 30rpx;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .popup-title {
      font-size: 32rpx;
      font-weight: 500;
      color: #333;
    }

    .close-btn {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;

      .close-text {
        font-size: 40rpx;
        color: #999;
        line-height: 1;
      }
    }
  }

  .filter-content {
    .filter-item {
      margin-bottom: 30rpx;

      .filter-label {
        display: block;
        font-size: 28rpx;
        color: #333;
        margin-bottom: 20rpx;
        font-weight: 500;
      }
    }
  }

  .type-options {
    display: flex;
    flex-direction: column;
    gap: 16rpx;

    .type-option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24rpx;
      background: #f8f9fa;
      border-radius: 12rpx;
      border: 2rpx solid transparent;

      &.active {
        background: #f0f7ff;
        border-color: #007aff;
      }

      .option-text {
        font-size: 28rpx;
        color: #333;
      }

      .check-icon {
        font-size: 24rpx;
        color: #007aff;
        font-weight: bold;
      }
    }
  }

  .popup-footer {
    margin-top: 40rpx;

    .btn-group {
      display: flex;
      gap: 24rpx;

      .btn {
        flex: 1;
        height: 88rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 12rpx;
        font-size: 28rpx;
        font-weight: 500;

        &.btn-cancel {
          background: #f8f9fa;
          color: #666;
        }

        &.btn-confirm {
          background: #007aff;
          color: #fff;
        }
      }
    }
  }
}
</style>
