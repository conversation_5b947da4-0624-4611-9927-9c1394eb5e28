<template>
  <BaseLayout nav-title="出勤打卡" :content-style="{ padding: '0' }">
    <view class="container">
      <!-- 班级信息和时间 -->
      <view class="header">
        <view class="class-info">
          <text class="class-name">{{ className }}</text>
        </view>
        <view class="date-container" @click="showDatePicker = true">
          <text class="date">{{ formatDate }}</text>
        </view>
      </view>

      <!-- 日期选择器组件 -->
      <up-datetime-picker
        :show="showDatePicker"
        v-model="selectedTimestamp"
        mode="date"
        :minDate="minDate"
        :maxDate="maxDate"
        @confirm="dateConfirm"
        @cancel="showDatePicker = false"
      ></up-datetime-picker>

      <!-- 统计信息 -->
      <view class="stats-section">
        <view class="stats-item">
          <text class="stats-label">总</text>
          <text class="stats-value">{{ attendanceStats.fullCount || 0 }}人</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">已入园</text>
          <text class="stats-value attended">{{ attendanceStats.presentCount || 0 }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">事假</text>
          <text class="stats-value leave">{{ attendanceStats.personalLeaveCount || 0 }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">病假</text>
          <text class="stats-value sick">{{ attendanceStats.sickLeaveCount || 0 }}</text>
        </view>
        <view class="stats-item">
          <text class="stats-label">停课</text>
          <text class="stats-value suspend">{{ attendanceStats.suspensionCount || 0 }}</text>
        </view>
      </view>

      <!-- 儿童列表 -->
      <view class="children-section">
        <view class="children-list">
          <view class="children-column left-column">
            <view
              v-for="(child, index) in leftColumnChildren"
              :key="'child-' + index"
              class="child-item"
              :class="{
                attended: child.status === 'attended',
                'personal-leave': child.status === 'personal-leave',
                'sick-leave': child.status === 'sick-leave',
                suspend: child.status === 'suspend',
                absent: child.status === 'absent'
              }"
              @click="handleChildClick(child)"
            >
              <view class="child-avatar">
                <image class="avatar-img" :src="getChildAvatar(child)" mode="aspectFill"></image>
              </view>
              <view class="child-info">
                <view class="child-name">{{ child.title }}</view>
                <view class="child-status">{{ getStatusText(child) }}</view>
              </view>
              <view class="status-icon" v-if="child.status === 'attended'">
                <text class="icon-check">✓</text>
              </view>
            </view>
          </view>
          <view class="children-column right-column">
            <view
              v-for="(child, index) in rightColumnChildren"
              :key="'child-' + index"
              class="child-item"
              :class="{
                attended: child.status === 'attended',
                'personal-leave': child.status === 'personal-leave',
                'sick-leave': child.status === 'sick-leave',
                suspend: child.status === 'suspend',
                absent: child.status === 'absent'
              }"
              @click="handleChildClick(child)"
            >
              <view class="child-avatar">
                <image class="avatar-img" :src="getChildAvatar(child)" mode="aspectFill"></image>
              </view>
              <view class="child-info">
                <view class="child-name">{{ child.title }}</view>
                <view class="child-status">{{ getStatusText(child) }}</view>
              </view>
              <view class="status-icon" v-if="child.status === 'attended'">
                <text class="icon-check">✓</text>
              </view>
            </view>
          </view>
        </view>

        <view class="empty-tip" v-if="childrenList.length === 0 && !isLoading"> 暂无儿童数据 </view>
      </view>

      <!-- 提示信息 -->
      <view class="tip-message" v-if="tipMessage">{{ tipMessage }}</view>
    </view>

    <!-- 底部固定按钮区域 -->
    <view class="bottom-submit">
      <view class="more-btn" @click="showMoreActions">
        <text class="more-text">更多</text>
      </view>
      <view class="submit-btn" @click="handleSubmitClick">
        <text class="submit-text">提交</text>
      </view>
    </view>

    <!-- 标记缺勤弹窗 -->
    <AbsenceModal
      :show="showLeaveModal"
      :selectedChild="selectedChild"
      @close="closeLeaveModal"
      @submit="handleAbsenceSubmit"
    />

    <!-- 标记停课弹窗 -->
    <up-popup :show="showSuspendModal" mode="bottom" round="20rpx" @close="closeSuspendModal">
      <view class="suspend-modal">
        <view class="modal-header">
          <text class="modal-title">标记停课</text>
        </view>
        <view class="modal-content">
          <view class="date-select-section">
            <text class="label">停课时间</text>
            <view class="date-input" @click="showSuspendDatePicker = true">
              <text class="date-text">{{ suspendDateText }}</text>
              <text class="arrow">></text>
            </view>
          </view>
          <view class="remark-section">
            <text class="label">停课备注</text>
            <textarea
              class="remark-input"
              v-model="suspendRemark"
              placeholder="请输入停课备注（选填）"
              maxlength="200"
              :show-confirm-bar="false"
            ></textarea>
          </view>
        </view>
        <view class="modal-footer">
          <view class="btn-cancel" @click="closeSuspendModal">
            <text class="btn-text">取消</text>
          </view>
          <view class="btn-confirm" @click="handleSuspendConfirm">
            <text class="btn-text">确定</text>
          </view>
        </view>
      </view>
    </up-popup>

    <!-- 停课日期选择器 -->
    <up-datetime-picker
      :show="showSuspendDatePicker"
      v-model="suspendTimestamp"
      mode="date"
      :minDate="minDate"
      :maxDate="maxDate"
      @confirm="suspendDateConfirm"
      @cancel="showSuspendDatePicker = false"
    ></up-datetime-picker>
  </BaseLayout>
</template>

<script setup>
import BaseLayout from '@/components/base-layout/base-layout.vue'
import AbsenceModal from './components/AbsenceModal.vue'
import { onLoad, onShow } from '@dcloudio/uni-app'
import { ref, computed } from 'vue'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
import { getChildrenAttendanceList, manualSave } from './api'
import { getclassList } from '@/api'

// 设置为中文
dayjs.locale('zh-cn')

// 页面状态
const className = ref('-')
const classId = ref('')
const childrenList = ref([])
const attendanceStats = ref({
  fullCount: 0,
  presentCount: 0,
  departedCount: 0,
  personalLeaveCount: 0,
  sickLeaveCount: 0,
  suspensionCount: 0
})
const isLoading = ref(false)
const tipMessage = ref('')
const showLeaveModal = ref(false)

const selectedChild = ref(null)

const showDatePicker = ref(false)
const selectedTimestamp = ref(Date.now())

// 停课相关状态
const showSuspendModal = ref(false)
const showSuspendDatePicker = ref(false)
const suspendTimestamp = ref(Date.now())
const suspendRemark = ref('')

// 设置最小日期为一年前，允许查看历史数据
const minDate = ref(dayjs().subtract(1, 'year').startOf('day').valueOf())
// 设置最大日期为今天
const maxDate = ref(dayjs().endOf('day').valueOf())

// 添加选择的日期字符串，用于API调用
const selectedDate = computed(() => {
  return dayjs(selectedTimestamp.value).format('YYYY-MM-DD')
})

// 格式化日期显示
const formatDate = computed(() => {
  const date = dayjs(selectedTimestamp.value)
  const weekDay = date.format('dddd')
  const dateStr = date.format('YYYY年MM月DD日')
  return `${dateStr} ${weekDay}`
})

// 停课日期显示文本
const suspendDateText = computed(() => {
  const date = dayjs(suspendTimestamp.value)
  return date.format('YYYY年MM月DD日')
})

// 停课日期字符串
const suspendDate = computed(() => {
  return dayjs(suspendTimestamp.value).format('YYYY-MM-DD')
})

// 左列儿童
const leftColumnChildren = computed(() => {
  return childrenList.value.filter((_, index) => index % 2 === 0)
})

// 右列儿童
const rightColumnChildren = computed(() => {
  return childrenList.value.filter((_, index) => index % 2 === 1)
})

// 获取儿童头像
const getChildAvatar = (child) => {
  // 优先使用 headers 中的头像
  if (child?.headers?.[0]?.uri) {
    return child.headers[0].uri + '?x-oss-process=image/resize,m_fill,w_600'
  }

  // 其次使用 childAvatar
  if (child.childAvatar) {
    return child.childAvatar + '?x-oss-process=image/resize,m_fill,w_600'
  }

  // 最后使用默认头像
  return child.sex === 2
    ? 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_girl.png'
    : 'https://pts-source.oss-cn-shenzhen.aliyuncs.com/vxmp/img/children_boy.png'
}

// 获取状态文本
const getStatusText = (child) => {
  // 根据原始状态描述显示具体文本
  switch (child.stateDesc) {
    case '出勤':
    case '已入园':
      return '已入园'
    case '事假':
      return '事假'
    case '病假':
      return '病假'
    case '离园':
      return '离园'
    case '停课':
      return '停课'
    case '无记录':
      return '未到'
    default:
      return '未到'
  }
}

// 获取儿童列表
const fetchChildrenList = async () => {
  try {
    isLoading.value = true
    const res = await getChildrenAttendanceList({
      attendanceDate: selectedDate.value,
      classId: classId.value
    })
    if (res && res.data && res.data.childDetailList) {
      // 处理新的数据结构
      childrenList.value = res.data.childDetailList.map((child) => ({
        childId: child.childId,
        title: child.childName,
        status: getChildStatus(child.stateDesc), // 根据 stateDesc 确定状态
        stateDesc: child.stateDesc, // 保存原始状态描述
        sex: child.sex || 1, // 使用实际性别，默认为男孩(1)
        childAvatar: child.childAvatar || '', // 使用实际头像
        headers: child.headers || [] // 使用实际头像数组
      }))
      attendanceStats.value = {
        fullCount: res.data.fullCount || 0,
        presentCount: res.data.presentCount || 0,
        departedCount: res.data.departedCount || 0,
        personalLeaveCount: res.data.personalLeaveCount || 0,
        sickLeaveCount: res.data.sickLeaveCount || 0,
        suspensionCount: res.data.suspensionCount || 0
      }
    }
  } catch (error) {
    console.error('获取儿童列表失败', error)
  } finally {
    isLoading.value = false
  }
}

// 根据状态描述确定儿童状态
const getChildStatus = (stateDesc) => {
  if (!stateDesc || stateDesc === '空值') {
    return 'absent' // 未到
  }

  switch (stateDesc) {
    case '出勤':
    case '已入园':
      return 'attended'
    case '无记录':
      return 'absent' // 无记录状态
    case '事假':
      return 'personal-leave' // 事假
    case '病假':
      return 'sick-leave' // 病假
    case '停课':
      return 'suspend' // 停课
    case '离园':
      return 'departed'
    default:
      return 'absent'
  }
}

// 处理儿童点击
const handleChildClick = async (child) => {
  // 所有状态的儿童都可以标记缺勤（更新状态）
  selectedChild.value = child
  showLeaveModal.value = true
}

// 判断选择的日期是否为当前日期
const isCurrentDate = () => {
  const today = dayjs().startOf('day')
  const selected = dayjs(selectedTimestamp.value).startOf('day')
  return selected.isSame(today, 'day')
}

// 构建 manualSave 参数，根据日期判断是否添加 attendanceDate
const buildManualSaveParams = (baseParams) => {
  const params = { ...baseParams }

  // 如果选择的不是当前日期，添加 attendanceDate 参数
  if (!isCurrentDate()) {
    const currentTime = dayjs().format('HH:mm:ss')
    params.attendanceDate = `${selectedDate.value} ${currentTime}`
  }

  return params
}

// 处理缺勤提交
const handleAbsenceSubmit = async (data) => {
  try {
    isLoading.value = true

    const baseParams = {
      userType: 0, // 儿童
      userIdList: [data.childId], // 单个儿童ID
      state: data.state,
      sickLeaveTypeCode: data.sickLeaveTypeCode || '',
      remark: data.remark || ''
    }

    const params = buildManualSaveParams(baseParams)
    const res = await manualSave(params)

    if (res.status === 0) {
      // 根据提交类型显示不同的成功消息
      if (data.submitType === 'status') {
        if (data.statusType === 'present') {
          tipMessage.value = '出勤标记成功'
        } else if (data.statusType === 'departed') {
          tipMessage.value = '离园标记成功'
        }
      } else {
        tipMessage.value = data.absenceType === 'personal' ? '事假标记成功' : '病假标记成功'
      }

      selectedChild.value.status = 'leave'
      await fetchChildrenList()
    } else {
      tipMessage.value = res.message || '标记失败'
    }
  } catch (error) {
    console.error('标记失败', error)
    tipMessage.value = '标记失败'
  } finally {
    isLoading.value = false
    closeLeaveModal()
    setTimeout(() => {
      tipMessage.value = ''
    }, 2000)
  }
}

// 关闭请假弹窗
const closeLeaveModal = () => {
  showLeaveModal.value = false
  selectedChild.value = null
}

// 获取无记录的儿童列表
const noRecordChildren = computed(() => {
  return childrenList.value.filter((child) => child.stateDesc === '无记录')
})

// 处理提交按钮点击
const handleSubmitClick = async () => {
  const noRecordList = noRecordChildren.value
  if (noRecordList.length === 0) {
    tipMessage.value = '儿童已全部入园'
    setTimeout(() => {
      tipMessage.value = ''
    }, 2000)
    return
  }

  // 直接批量标记所有无记录儿童为出勤
  const noRecordIds = noRecordList.map((child) => child.childId)
  await handleBatchAttendance(noRecordIds)
}

// 处理批量出勤标记
const handleBatchAttendance = async (selectedIds) => {
  try {
    isLoading.value = true

    const baseParams = {
      userType: 0, // 儿童
      userIdList: selectedIds,
      state: 1, // 出勤
      sickLeaveTypeCode: '',
      remark: ''
    }

    const params = buildManualSaveParams(baseParams)
    const res = await manualSave(params)

    if (res.status === 0) {
      const count = selectedIds.length
      tipMessage.value = `${count}名儿童出勤标记成功`
      // 刷新页面数据
      await fetchChildrenList()
      console.log('批量出勤成功，已刷新数据')

      // 延迟返回上一页面
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    } else {
      tipMessage.value = res.message || '标记失败'
    }
  } catch (error) {
    console.error('批量出勤标记失败', error)
    tipMessage.value = '标记失败'
  } finally {
    isLoading.value = false
    setTimeout(() => {
      tipMessage.value = ''
    }, 2000)
  }
}

// 显示更多操作菜单
const showMoreActions = () => {
  uni.showActionSheet({
    itemList: ['标记停课'],
    success: (res) => {
      if (res.tapIndex === 0) {
        // 标记停课
        showSuspendModal.value = true
      }
    }
  })
}

// 关闭停课弹窗
const closeSuspendModal = () => {
  showSuspendModal.value = false
  suspendRemark.value = '' // 清空备注
}

// 停课日期确认
const suspendDateConfirm = (event) => {
  let timestamp
  if (typeof event === 'object' && event !== null && 'value' in event) {
    timestamp = event.value
  } else {
    timestamp = event
  }

  if (timestamp && !isNaN(timestamp)) {
    suspendTimestamp.value = timestamp
  } else {
    suspendTimestamp.value = Date.now()
  }

  showSuspendDatePicker.value = false
}

// 处理停课确认
const handleSuspendConfirm = async () => {
  try {
    isLoading.value = true

    // 获取所有儿童ID
    const allChildrenIds = childrenList.value.map((child) => child.childId)

    const currentTime = dayjs().format('HH:mm:ss')
    const params = {
      userType: 0, // 儿童
      userIdList: allChildrenIds, // 所有儿童ID
      state: 6, // 停课状态
      attendanceDate: `${suspendDate.value} ${currentTime}`,
      sickLeaveTypeCode: '',
      remark: suspendRemark.value || ''
    }

    const res = await manualSave(params)

    if (res.status === 0) {
      tipMessage.value = '停课标记成功'
      await fetchChildrenList()
      closeSuspendModal()

      // 延迟返回上一页面
      setTimeout(() => {
        uni.navigateBack()
      }, 1000)
    } else {
      tipMessage.value = res.message || '停课标记失败'
    }
  } catch (error) {
    console.error('停课标记失败', error)
    tipMessage.value = '停课标记失败'
  } finally {
    isLoading.value = false
    setTimeout(() => {
      tipMessage.value = ''
    }, 2000)
  }
}

// 日期确认事件
const dateConfirm = (event) => {
  console.log('Date picker confirmed with event:', event)

  // 确保timestamp是有效的 - 处理对象格式 {value: timestamp, mode: "date"}
  let timestamp
  if (typeof event === 'object' && event !== null && 'value' in event) {
    timestamp = event.value
  } else {
    timestamp = event
  }

  if (timestamp && !isNaN(timestamp)) {
    selectedTimestamp.value = timestamp
    console.log('Selected timestamp set to:', selectedTimestamp.value)
    console.log('Formatted date:', selectedDate.value)
  } else {
    console.error('Invalid timestamp received:', event)
    // 设置为当前日期
    selectedTimestamp.value = Date.now()
  }

  showDatePicker.value = false

  // 添加短暂延迟确保UI更新后再获取数据
  setTimeout(() => {
    fetchChildrenList()
  }, 100)
}

// 获取班级名称
const getClassName = async () => {
  try {
    const userInfo = uni.getStorageSync('USER_INFO')
    const res = await getclassList({ schoolId: userInfo.currentSchoolId })
    if (res.status === 0 && res.data) {
      const classInfo = res.data.find((item) => item.id === classId.value)
      if (classInfo) {
        className.value = classInfo.title
      }
    }
  } catch (error) {
    console.error('获取班级名称失败:', error)
  }
}

// 页面加载
onLoad(async () => {
  const userInfo = uni.getStorageSync('USER_INFO')
  classId.value = userInfo.currentClassId

  // 获取班级名称
  await getClassName()

  if (classId.value) {
    fetchChildrenList()
  } else {
    tipMessage.value = '缺少班级参数'
    setTimeout(() => {
      tipMessage.value = ''
    }, 2000)
  }
})

// 页面显示时更新数据
onShow(async () => {
  const userInfo = uni.getStorageSync('USER_INFO')

  // 检查班级是否发生变化
  if (userInfo.currentClassId !== classId.value) {
    classId.value = userInfo.currentClassId
    // 更新班级名称
    await getClassName()
    // 重新获取数据
    fetchChildrenList()
  }
})
</script>
<style lang="scss" scoped>
.container {
  // background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 152rpx; /* 为底部按钮留出空间 */
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  .class-info {
    .class-name {
      font-size: 34rpx;
      color: #333;
      font-weight: 600;
    }
  }

  .date-container {
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    padding: 8rpx 16rpx;
    border-radius: 8rpx;

    .date {
      font-size: 28rpx;
      color: rgba(128, 128, 128, 1);
      font-weight: normal;
    }
  }
}

.stats-section {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 32rpx;
  .stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8rpx;

    .stats-label {
      font-size: 24rpx;
      color: #666;
    }

    .stats-value {
      font-size: 32rpx;
      font-weight: 600;
      color: #333;

      &.attended {
        color: #52c41a;
      }

      &.leave {
        color: #faad14;
      }

      &.sick {
        color: #ff4d4f;
      }

      &.suspend {
        color: #722ed1;
      }
    }
  }
}

.children-section {
  padding: 0 32rpx;

  .children-list {
    display: flex;
    gap: 30rpx;

    .children-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      gap: 20rpx;
    }

    .child-item {
      display: flex;
      align-items: center;
      padding: 20rpx;
      background-color: #fff;
      border-radius: 24rpx;
      box-shadow: 2rpx 4rpx 6rpx rgba(0, 0, 0, 0.02);
      position: relative;

      &.attended {
        border-left: 6rpx solid #52c41a;
      }

      &.personal-leave {
        border-left: 10rpx solid #faad14; // 事假 - 橘色，加宽边框
      }

      &.sick-leave {
        border-left: 10rpx solid #ff4d4f; // 病假 - 红色，加宽边框
      }

      &.suspend {
        border-left: 10rpx solid #722ed1; // 停课 - 紫色，加宽边框
      }

      // &.absent {
      //   border-left: 6rpx solid #d9d9d9;
      // }

      .child-avatar {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
        overflow: hidden;
        margin-right: 20rpx;

        .avatar-img {
          width: 100%;
          height: 100%;
        }
      }

      .child-info {
        flex: 1;

        .child-name {
          font-size: 30rpx;
          font-weight: 600;
          color: #333;
          margin-bottom: 6rpx;
        }

        .child-status {
          font-size: 24rpx;
          color: #666;
        }
      }

      .status-icon {
        .icon-check {
          color: #52c41a;
          font-size: 32rpx;
          font-weight: bold;
        }
      }
    }
  }

  .empty-tip {
    text-align: center;
    padding: 60rpx 0;
    color: #999;
    font-size: 28rpx;
  }
}

.tip-message {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 20rpx 30rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  z-index: 999;
}

.bottom-submit {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 32rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: flex;
  gap: 24rpx;

  .more-btn {
    width: 160rpx;
    height: 88rpx;
    background: #f5f5f5;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .more-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #666;
    }
  }

  .submit-btn {
    flex: 1;
    height: 88rpx;
    background: #1890ff;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;

    .submit-text {
      font-size: 32rpx;
      font-weight: 600;
      color: #fff;
    }
  }
}

// 停课弹窗样式
.suspend-modal {
  width: 100%;
  background: #fff;
  border-radius: 20rpx 20rpx 0 0;
  overflow: hidden;
  padding-bottom: env(safe-area-inset-bottom);

  .modal-header {
    padding: 40rpx 32rpx 20rpx;
    text-align: center;
    border-bottom: 1rpx solid #f0f0f0;

    .modal-title {
      font-size: 36rpx;
      font-weight: 600;
      color: #333;
    }
  }

  .modal-content {
    padding: 40rpx 32rpx;

    .date-select-section {
      margin-bottom: 32rpx;

      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 20rpx;
        display: block;
      }

      .date-input {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 24rpx 20rpx;
        background: #f8f8f8;
        border-radius: 12rpx;
        border: 1rpx solid #e8e8e8;

        .date-text {
          font-size: 30rpx;
          color: #333;
        }

        .arrow {
          font-size: 24rpx;
          color: #999;
        }
      }
    }

    .remark-section {
      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 20rpx;
        display: block;
      }

      .remark-input {
        width: 100%;
        min-height: 120rpx;
        padding: 20rpx;
        background: #f8f8f8;
        border-radius: 12rpx;
        border: 1rpx solid #e8e8e8;
        font-size: 28rpx;
        color: #333;
        line-height: 1.5;
        box-sizing: border-box;
        resize: none;

        &::placeholder {
          color: #999;
        }
      }
    }
  }

  .modal-footer {
    display: flex;
    padding: 32rpx;
    gap: 24rpx;

    .btn-cancel,
    .btn-confirm {
      flex: 1;
      height: 88rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 12rpx;
      transition: all 0.3s ease;

      .btn-text {
        font-size: 32rpx;
        font-weight: 600;
      }

      &:active {
        transform: scale(0.98);
      }
    }

    .btn-cancel {
      background: #f5f5f5;
      border: 1rpx solid #e8e8e8;

      .btn-text {
        color: #666;
      }

      &:active {
        background: #e8e8e8;
      }
    }

    .btn-confirm {
      background: #1890ff;

      .btn-text {
        color: #fff;
      }

      &:active {
        background: #0f7ae5;
      }
    }
  }
}
</style>
