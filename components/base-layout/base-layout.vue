<template>
  <view class="base-layout-container" :class="containerClass">
    <!-- Navbar 区域 -->
    <up-navbar
      :title="navTitle"
      :safeAreaInsetTop="safeAreaInsetTop"
      :fixed="navFixed"
      :placeholder="navPlaceholder"
      :bgColor="navBgColor"
      :autoBack="autoBack"
      @leftClick="handleNavBack"
      :class="navClass"
    >
    </up-navbar>

    <slot name="extra"></slot>
    <!-- 内容滚动区域 -->
    <scroll-view
      class="content-scroll-view"
      :class="scrollClass"
      :scroll-y="scrollEnabled"
      :scroll-top="scrollTop"
      :scroll-into-view="scrollIntoView"
      enable-flex
      :scroll-with-animation="scrollWithAnimation"
      :style="contentStyle"
      :refresher-enabled="refresherEnabled"
      :refresher-triggered="refresherTriggered"
      @scrolltolower="handleScrollToLower"
      @refresherrefresh="handleRefresherRefresh"
    >
      <slot></slot>
    </scroll-view>

    <!-- Footer 底部区域 -->
    <view class="footer-container" :style="footerStyle">
      <slot name="footer"></slot>
    </view>
  </view>
</template>

<script setup>
import { getCurrentInstance } from 'vue'
const props = defineProps({
  // container
  containerClass: {
    type: String,
    default: ''
  },
  // Navbar 相关 Props
  navTitle: String,
  safeAreaInsetTop: {
    type: Boolean,
    default: true
  },
  navFixed: {
    type: Boolean,
    default: true
  },
  navPlaceholder: {
    type: Boolean,
    default: true
  },
  navBgColor: {
    type: String,
    default: 'transparent'
  },
  autoBack: {
    type: Boolean,
    default: true
  },
  navClass: String,

  // ScrollView 相关 Props
  scrollEnabled: {
    type: Boolean,
    default: true
  },
  scrollTop: [Number, String],
  scrollIntoView: String,
  scrollWithAnimation: {
    type: Boolean,
    default: true
  },
  contentStyle: [Object, String],
  scrollClass: String,
  handleScrollToLower: {
    type: Function
  },

  // 下拉刷新相关 Props
  refresherEnabled: {
    type: Boolean,
    default: false
  },
  refresherTriggered: {
    type: Boolean,
    default: false
  },

  // Footer 相关 Props
  footerStyle: [Object, String]
})

const emit = defineEmits(['leftClick', 'scrolltolower', 'refresherrefresh'])

const handleNavBack = () => {
  emit('leftClick')
}

const handleScrollToLower = () => {
  emit('scrolltolower')
}

const handleRefresherRefresh = () => {
  emit('refresherrefresh')
}

defineExpose({
  $scope: getCurrentInstance()
})
</script>

<style lang="scss" scoped>
.base-layout-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f5f5 url('https://c.mypacelab.com/vxmp/img/history_background_3x.png') no-repeat
    center top;
  background-size: contain;
  /* 让图片宽度撑满，自动计算高度 */

  .content-scroll-view {
    flex: 1;
    min-height: 0; // 修复 flex 滚动问题
    padding: 20rpx 32rpx;
    box-sizing: border-box;
  }

  .footer-container {
    padding: 20rpx 32rpx;
  }
}
.newBg {
  background: url('https://c.mypacelab.com/vxmp/img/history_background_3x.png') no-repeat center top;
  background-size: 100% 100%;
}
</style>
